package com.sgmw.ksongs.ui.search

import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView

class SearchResultPagerAdapter(private val views: List<View>, private val tabs: List<String>) :
    RecyclerView.Adapter<SearchResultPagerAdapter.ViewHolder>() {

    class ViewHolder(val containerView: FrameLayout) : RecyclerView.ViewHolder(containerView)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        // 创建一个容器来包装我们的View，避免View被多次添加到不同父容器的问题
        val container = FrameLayout(parent.context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }

        return ViewHolder(container)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val targetView = views[position]
        val container = holder.containerView

        // 清空容器中的所有子View
        container.removeAllViews()

        // 如果目标View已经有父容器，先从父容器中移除
        (targetView.parent as? ViewGroup)?.removeView(targetView)

        // 确保目标View的布局参数正确
        targetView.layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )

        // 将目标View添加到容器中
        container.addView(targetView)
    }

    override fun getItemCount(): Int {
        return views.size
    }

    override fun getItemViewType(position: Int): Int {
        // 使用position作为viewType，确保每个位置都有唯一的ViewHolder
        return position
    }

    fun getPageTitle(position: Int): CharSequence? {
        return if (position < tabs.size) tabs[position] else null
    }
}