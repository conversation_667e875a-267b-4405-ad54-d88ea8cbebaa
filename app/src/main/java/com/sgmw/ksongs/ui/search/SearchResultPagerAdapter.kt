package com.sgmw.ksongs.ui.search

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

class SearchResultPagerAdapter(private val views: List<View>, private val tabs: List<String>) :
    RecyclerView.Adapter<SearchResultPagerAdapter.ViewHolder>() {

    class ViewHolder(val view: View) : RecyclerView.ViewHolder(view)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        // ViewPager2会为每个页面创建ViewHolder，我们直接返回对应的View
        return ViewHolder(views[viewType])
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        // ViewPager2中不需要特殊的绑定逻辑，View已经在ViewHolder中了
    }

    override fun getItemCount(): Int {
        return views.size
    }

    override fun getItemViewType(position: Int): Int {
        // 使用position作为viewType，确保每个位置都有唯一的ViewHolder
        return position
    }

    fun getPageTitle(position: Int): CharSequence? {
        return if (position < tabs.size) tabs[position] else null
    }
}