package com.sgmw.ksongs.ui.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.Fragment

/**
 * 搜索结果Fragment，用于包装SearchSongsResultView和SearchSingersResultView
 * 使其能够在ViewPager2中正常工作
 */
class SearchResultFragment : Fragment() {

    private var resultView: View? = null

    companion object {
        private const val ARG_VIEW_TYPE = "view_type"
        private const val VIEW_TYPE_SONGS = 0
        private const val VIEW_TYPE_SINGERS = 1

        fun newInstance(view: SearchSongsResultView): SearchResultFragment {
            return SearchResultFragment().apply {
                resultView = view
            }
        }

        fun newInstance(view: SearchSingersResultView): SearchResultFragment {
            return SearchResultFragment().apply {
                resultView = view
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 创建一个FrameLayout作为容器
        val frameLayout = FrameLayout(requireContext()).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }

        // 将传入的View添加到容器中
        resultView?.let { view ->
            // 如果View已经有父容器，先移除
            (view.parent as? ViewGroup)?.removeView(view)
            
            // 设置正确的布局参数
            view.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
            
            frameLayout.addView(view)
        }

        return frameLayout
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 清理View引用，防止内存泄露
        resultView?.let { view ->
            (view.parent as? ViewGroup)?.removeView(view)
        }
    }
}
