package com.sgmw.ksongs.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

/**
 * 安全的ViewPager，解决多指滑动时的crash问题
 * 主要解决：java.lang.IllegalArgumentException: pointerIndex out of range
 */
class SafeViewPager @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ViewPager(context, attrs) {

    override fun onInterceptTouchEvent(ev: MotionEvent?): Bo<PERSON>an {
        return try {
            super.onInterceptTouchEvent(ev)
        } catch (ex: IllegalArgumentException) {
            // 捕获多指滑动时的pointerIndex out of range异常
            false
        } catch (ex: ArrayIndexOutOfBoundsException) {
            // 捕获可能的数组越界异常
            false
        }
    }

    override fun onTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        return try {
            super.onTouchEvent(ev)
        } catch (ex: IllegalArgumentException) {
            // 捕获多指滑动时的pointerIndex out of range异常
            false
        } catch (ex: ArrayIndexOutOfBoundsException) {
            // 捕获可能的数组越界异常
            false
        }
    }
}
