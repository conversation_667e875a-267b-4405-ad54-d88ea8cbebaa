package com.sgmw.ksongs.ui.search

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter

class SearchResultPagerAdapter(
    fragmentActivity: FragmentActivity,
    private val tabs: List<String>
) : FragmentStateAdapter(fragmentActivity) {

    override fun getItemCount(): Int = 2

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> SearchResultFragment.newInstanceForSongs()
            1 -> SearchResultFragment.newInstanceForSingers()
            else -> throw IllegalArgumentException("Invalid position: $position")
        }
    }

    fun getPageTitle(position: Int): CharSequence? {
        return if (position < tabs.size) tabs[position] else null
    }
}