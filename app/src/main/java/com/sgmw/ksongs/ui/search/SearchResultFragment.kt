package com.sgmw.ksongs.ui.search

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.Fragment

/**
 * 搜索结果Fragment，用于包装SearchSongsResultView和SearchSingersResultView
 * 使其能够在ViewPager2中正常工作
 */
class SearchResultFragment : Fragment() {

    companion object {
        private const val ARG_VIEW_TYPE = "view_type"
        private const val VIEW_TYPE_SONGS = 0
        private const val VIEW_TYPE_SINGERS = 1

        fun newInstanceForSongs(): SearchResultFragment {
            return SearchResultFragment().apply {
                arguments = Bundle().apply {
                    putInt(ARG_VIEW_TYPE, VIEW_TYPE_SONGS)
                }
            }
        }

        fun newInstanceForSingers(): SearchResultFragment {
            return SearchResultFragment().apply {
                arguments = Bundle().apply {
                    putInt(ARG_VIEW_TYPE, VIEW_TYPE_SINGERS)
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // 创建一个FrameLayout作为容器
        val frameLayout = FrameLayout(requireContext()).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        }

        // 根据类型获取对应的View
        val viewType = arguments?.getInt(ARG_VIEW_TYPE, VIEW_TYPE_SONGS) ?: VIEW_TYPE_SONGS
        val searchHomeFragment = findSearchHomeFragment()

        val resultView = when (viewType) {
            VIEW_TYPE_SONGS -> searchHomeFragment?.getSongsResultView()
            VIEW_TYPE_SINGERS -> searchHomeFragment?.getSingersResultView()
            else -> null
        }

        // 将View添加到容器中
        resultView?.let { view ->
            // 如果View已经有父容器，先移除
            (view.parent as? ViewGroup)?.removeView(view)

            // 设置正确的布局参数
            view.layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            frameLayout.addView(view)
        }

        return frameLayout
    }

    private fun findSearchHomeFragment(): SearchHomeFragment? {
        // 通过activity的fragment manager递归查找SearchHomeFragment
        return findFragmentRecursively(activity?.supportFragmentManager?.fragments)
    }

    private fun findFragmentRecursively(fragments: List<Fragment>?): SearchHomeFragment? {
        fragments?.forEach { fragment ->
            if (fragment is SearchHomeFragment) {
                return fragment
            }
            // 递归查找子Fragment
            val childResult = findFragmentRecursively(fragment.childFragmentManager.fragments)
            if (childResult != null) {
                return childResult
            }
        }
        return null
    }
}
