package com.sgmw.ksongs.ui.search

import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView

class SearchResultPagerAdapter(private val views: List<View>, private val tabs: List<String>) :
    RecyclerView.Adapter<SearchResultPagerAdapter.ViewHolder>() {

    class ViewHolder(val view: View) : RecyclerView.ViewHolder(view)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = views[viewType]

        // 确保View的布局参数符合ViewPager2的要求（必须填满整个ViewPager2）
        view.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )

        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        // ViewPager2中不需要特殊的绑定逻辑，View已经在ViewHolder中了
        // 但我们需要确保每次绑定时布局参数都是正确的
        holder.view.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
    }

    override fun getItemCount(): Int {
        return views.size
    }

    override fun getItemViewType(position: Int): Int {
        // 使用position作为viewType，确保每个位置都有唯一的ViewHolder
        return position
    }

    fun getPageTitle(position: Int): CharSequence? {
        return if (position < tabs.size) tabs[position] else null
    }
}