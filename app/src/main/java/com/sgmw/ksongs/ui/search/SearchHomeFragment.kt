package com.sgmw.ksongs.ui.search

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.GONE
import android.view.View.OnTouchListener
import android.view.View.VISIBLE
import android.view.inputmethod.EditorInfo
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.TooltipCompat
import androidx.navigation.Navigation
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.blankj.utilcode.util.SizeUtils
import com.google.android.flexbox.FlexWrap
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameFragment
import com.sgmw.common.utils.EventBusUtils
import com.sgmw.common.utils.Log
import com.sgmw.ksongs.R
import com.sgmw.ksongs.databinding.FragmentSearchHomeBinding
import com.sgmw.ksongs.databinding.LayoutHotSearchHeaderBinding
import com.sgmw.ksongs.databinding.LayoutSuggestionSearchHeaderBinding
import com.sgmw.ksongs.model.bean.RankingsBean
import com.sgmw.ksongs.model.bean.Operation
import com.sgmw.ksongs.model.bean.SearchResultBean
import com.sgmw.ksongs.model.bean.SingerBean
import com.sgmw.ksongs.model.bean.SongInfoBean

import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.eventbus.PlayControlEvent
import com.sgmw.ksongs.track.BigDataConstants
import com.sgmw.ksongs.track.BigDataConstants.SPLIT
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.adapter.RankListAdapter
import com.sgmw.ksongs.ui.adapter.SearchHistoryAdapter
import com.sgmw.ksongs.ui.adapter.SearchSuggestionSingerAdapter
import com.sgmw.ksongs.ui.dialog.ConfirmDialogFragment
import com.sgmw.ksongs.ui.singerlist.SongListBySingerFragment
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager

import com.sgmw.ksongs.utils.InputMethodUtils
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.showToast

import com.sgmw.ksongs.viewmodel.search.SearchHomeViewModel
import com.sgmw.ksongs.widget.AccessibilityGridLayoutManager
import com.sgmw.ksongs.widget.AccessibilityLinearLayoutManager
import com.sgmw.ksongs.widget.SpaceItemDecoration


class SearchHomeFragment : BaseFrameFragment<FragmentSearchHomeBinding, SearchHomeViewModel>() {

    // 返回键回调，用于控制返回键行为
    private var backPressedCallback: OnBackPressedCallback? = null
    private val mSuggestionSingerAdapter: SearchSuggestionSingerAdapter by lazy {
        SearchSuggestionSingerAdapter()
    }

    private lateinit var mHotSearchHeaderBinding: LayoutHotSearchHeaderBinding
    private lateinit var mSearchSuggestionHeaderBinding: LayoutSuggestionSearchHeaderBinding


    private val mSuggestionSongAdapter: RankListAdapter by lazy {
        RankListAdapter()
    }

    private val mSearchHistoryAdapter: SearchHistoryAdapter by lazy {
        SearchHistoryAdapter()
    }

    private val mSearchHotAdapter: RankListAdapter by lazy {
        RankListAdapter()
    }

    private var mSongsResultView: SearchSongsResultView? = null

    private var mSingerResultView: SearchSingersResultView? = null

    // TabLayoutMediator用于连接TabLayout和ViewPager2
    private var tabLayoutMediator: TabLayoutMediator? = null

    //当前显示的界面：主页：1 建议页：2 结果页：3
    private var showType: Int = 1

    //是否点击了搜索按钮
    private var isSearchClick = false

    // 提供给SearchResultFragment访问View的方法
    fun getSongsResultView(): SearchSongsResultView? = mSongsResultView
    fun getSingersResultView(): SearchSingersResultView? = mSingerResultView

    override fun FragmentSearchHomeBinding.initView() {
        Log.d(TAG, "initView")
        mBinding?.let { binding ->
            mViewModel?.let { viewModel ->

                binding.ivBack.setOnSingleClickListener {
                    goBack()
                }

                initSearchBoxView(binding, viewModel)

                initHotSearchView()

                showSearchMain(binding)

                binding.btnSearch.setOnSingleClickListener {
                    jumpToSearchResult()
                }

                binding.layoutSearchBox.setOnTouchListener(object : OnTouchListener {
                    override fun onTouch(p0: View?, p1: MotionEvent?): Boolean {
                        hideHistoryDelete()
                        return true
                    }
                })

//                binding.layoutSuggestionResult.setOnTouchListener(object : OnTouchListener {
//                    override fun onTouch(p0: View?, p1: MotionEvent?): Boolean {
//                        hideHistoryDelete()
//                        return true
//                    }
//                })


            }
            binding.searchRoot.setOnTouchListener { view, motionEvent ->
                hideInputMethod()
                false
            }

        }

    }

    private fun setHotSearchErrorEntryListener() {
        mBinding?.slHotSearch?.setErrorRetryClickListener {
            mBinding?.llHotSearch?.removeView(mHotSearchHeaderBinding.root)
            mBinding?.slHotSearch?.showLoading()
            mViewModel?.getHotSearchList(Operation.NewData)

        }
    }

    private fun setSearchResultErrorEntryListener() {
        mBinding?.slSearchResult?.setErrorRetryClickListener {
            jumpToSearchResult(mBinding?.etSearchBox?.text.toString())
        }
    }

    fun hideHistoryDelete() {
        if (mSearchHistoryAdapter.isDeleteVisible() == VISIBLE) {
            mSearchHistoryAdapter.setDeleteVisible(GONE)
        }
    }

    private fun initSearchBoxView(
        binding: FragmentSearchHomeBinding,
        viewModel: SearchHomeViewModel
    ) {
        binding.etSearchBox.setOnSingleClickListener {
            hideHistoryDelete()
        }
        binding.etSearchBox.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int
            ) {

            }

            override fun onTextChanged(
                s: CharSequence?,
                start: Int,
                before: Int,
                count: Int
            ) {
                Log.d(TAG, "onTextChanged")
                if (isSearchClick) {//点击搜索按钮后
                    isSearchClick = false
                    jumpToSearchResult(s.toString())
                } else {
                    if (s.toString().isEmpty()) {
                        Log.d(TAG, "onTextChanged null")
                        binding.etSearchBox.hint = viewModel.mHintText.value
                        binding.ivSearchBoxClear.visibility = GONE
                        viewModel.mSearchText = null
                        showSearchMain(binding)
                    } else {
                        binding.ivSearchBoxClear.visibility = VISIBLE
                        viewModel.getSuggestionResult(s.toString(), Operation.NewData)
                    }

                }

            }

            override fun afterTextChanged(s: Editable?) {

            }
        })

        binding.etSearchBox.setOnEditorActionListener { v, actionId, event ->
            Log.d(TAG, "onEditorAction $actionId")
            if (EditorInfo.IME_ACTION_SEARCH == actionId) {
                jumpToSearchResult()
            }
            false
        }

        binding.etSearchBox.setOnFocusChangeListener { view, focus ->
            Log.d(TAG, "onFocusChange $focus")
            if (focus) {
                showInputMethod()
            } else {
                hideInputMethod()
            }
        }

        binding.ivSearchBoxClear.setOnSingleClickListener {
            clearInputText()
            showSearchMain(binding)
        }
    }

    private fun jumpToSearchResult() {
        Log.d(TAG, "jumpToSearchResult")
        isSearchClick = true
        mBinding?.let { binding ->
            mViewModel?.let { viewModel ->
                var searchText = binding.etSearchBox.text.toString()
                if (searchText.isEmpty()) {
                    val hint = viewModel.mHintText.value
                    if (!hint.isNullOrEmpty()) {
                        searchText = hint
                    }
                }
                SensorsDataManager.trackSearchEvent(
                    BigDataConstants.CARD_NAME_SEARCH, searchText
                )
                binding.etSearchBox.setText(searchText)
            }
        }
    }

    private fun jumpToSearchResult(searchText: String) {
        if (searchText.trim().isEmpty()) {
            showToast(R.string.search_no_content_tips)
            return
        }
        mBinding?.let { binding ->
            mViewModel?.let { viewModel ->
                Log.d(TAG, "jumpToSearchResult")
                binding.ivSearchBoxClear.visibility = GONE
                showSearchResult(binding)
                binding.slSearchResult.showLoading()
                viewModel.getSearchResult(searchText, Operation.NewData)
                viewModel.saveHistoryWords(searchText)
                hideInputMethod()
            }

        }

    }

    private fun showSearchMain(binding: FragmentSearchHomeBinding) {
        binding.refreshLayoutHotSearch.visibility = VISIBLE
        binding.refreshLayoutSuggestionSongs.visibility = GONE
        binding.slSearchResult.visibility = GONE
        showType = 1

        mSearchHotAdapter?.notifyDataSetChanged()
    }

    private fun showSuggestionSearchResult(binding: FragmentSearchHomeBinding) {
        binding.refreshLayoutHotSearch.visibility = GONE
        binding.slSearchResult.visibility = GONE
        binding.refreshLayoutSuggestionSongs.visibility = VISIBLE
        showType = 2

        mSuggestionSongAdapter?.notifyDataSetChanged()
    }

    private fun showSearchResult(binding: FragmentSearchHomeBinding) {
        binding.slSearchResult.visibility = VISIBLE
        binding.refreshLayoutSuggestionSongs.visibility = GONE
        binding.refreshLayoutHotSearch.visibility = GONE
        showType = 3

        mSongsResultView?.notifyDataSetChanged()
    }

    private fun hideSearchHistoryView(binding: FragmentSearchHomeBinding) {
        mHotSearchHeaderBinding.tvHistorySearch.visibility = GONE
        mHotSearchHeaderBinding.ivHistoryClear.visibility = GONE
        mHotSearchHeaderBinding.recyclerviewHistorySearch.visibility = GONE
        mHotSearchHeaderBinding.viewDivider.visibility = GONE
    }

    private fun showSearchHistoryView(
        binding: FragmentSearchHomeBinding,
        viewModel: SearchHomeViewModel,
        historyList: MutableList<String>
    ) {

        if (mHotSearchHeaderBinding.recyclerviewHistorySearch.adapter == null) {
            mHotSearchHeaderBinding.ivHistoryClear.setOnSingleClickListener {
                hideHistoryDelete()
                hideInputMethod()
                val confirmDialogFragment = ConfirmDialogFragment()
                confirmDialogFragment.setTitle(requireContext().getString(R.string.search_delete_title))
                confirmDialogFragment.setMessage(requireContext().getString(R.string.search_delete_message))
                // 检查 ConfirmDialogFragment 是否已经显示
                val fragment = parentFragmentManager.findFragmentByTag("ConfirmDialogFragment")
                if (fragment == null) {
                    // 弹出 ConfirmDialogFragment
                    confirmDialogFragment.show(parentFragmentManager, "ConfirmDialogFragment")
                    // 添加点击事件
                    confirmDialogFragment.setOnConfirmListener {
                        confirmDialogFragment.dismiss()
                        viewModel.clearHistoryWords()
                    }
                }
            }

            val layoutManagerHistory =
                LimitedRowFlexboxLayoutManager(context, 2)
            layoutManagerHistory.flexWrap = FlexWrap.WRAP
            mHotSearchHeaderBinding.recyclerviewHistorySearch.layoutManager = layoutManagerHistory
            mHotSearchHeaderBinding.recyclerviewHistorySearch.adapter = mSearchHistoryAdapter
            mHotSearchHeaderBinding.recyclerviewHistorySearch.addItemDecoration(
                SpaceItemDecoration(
                    rightSpace = SizeUtils.dp2px(20F)
                )
            )

            mSearchHistoryAdapter.setNewInstance(historyList)

            mSearchHistoryAdapter.setOnItemClickListener { adapter, v, position ->
                Log.d(TAG, "mSearchHistoryAdapter.setOnItemClickListener")
                hideHistoryDelete()
                isSearchClick = true
                val item = adapter.data[position] as String
                SensorsDataManager.trackSearchEvent(
                    BigDataConstants.CARD_NAME_HISTORY_SEARCH, item
                )
                binding.etSearchBox.setText(item)
                binding.etSearchBox.setSelection(item.length)
            }

            mSearchHistoryAdapter.addChildClickViewIds(R.id.iv_search_history_delete)
            mSearchHistoryAdapter.setOnItemChildClickListener { adapter, v, position ->
                if (v.id == R.id.iv_search_history_delete) {
                    viewModel.deleteHistoryWords(adapter.data[position] as String)
                }
            }

            mSearchHistoryAdapter.setOnItemLongClickListener { adapter, v, position ->
                Log.d(TAG, "mSearchHistoryAdapter.setOnItemLongClickListener")
                if (mSearchHistoryAdapter.isDeleteVisible() == GONE) {
                    mSearchHistoryAdapter.setDeleteVisible(VISIBLE)
                }
                true
            }
        } else {
            mSearchHistoryAdapter.setList(historyList)
        }

        mHotSearchHeaderBinding.tvHistorySearch.visibility = VISIBLE
        mHotSearchHeaderBinding.ivHistoryClear.visibility = VISIBLE
        mHotSearchHeaderBinding.recyclerviewHistorySearch.visibility = VISIBLE
        mHotSearchHeaderBinding.viewDivider.visibility = VISIBLE
    }

    private fun initHotSearchView() {
        mBinding?.let { binding ->
            binding.recyclerviewHotSearch.addOnScrollListener(object : OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                        hideInputMethod()
                    }
                }
            })
            val layoutManagerSong =
                AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            binding.recyclerviewHotSearch.layoutManager = layoutManagerSong
            binding.recyclerviewHotSearch.adapter = mSearchHotAdapter
            mHotSearchHeaderBinding = LayoutHotSearchHeaderBinding.inflate(
                LayoutInflater.from(context),
                mBinding?.root,
                false
            )
            mSearchHotAdapter.addHeaderView(mHotSearchHeaderBinding.root)
            mSearchHotAdapter.setCardName(getString(R.string.hot_search))
            mSearchHotAdapter.setOnItemClickListener { _, view, position ->
                hideInputMethod()
                KaraokePlayerManager.playSong(this, mSearchHotAdapter.getItem(position), getString(R.string.hot_search))
            }

            binding.refreshLayoutHotSearch.setOnLoadMoreListener {
                mViewModel?.let { viewModel ->
                    if (!viewModel.getHotSearchList(Operation.LoadMore)) {
                        it.finishLoadMoreWithNoMoreData()
                    }
                }
            }
        }

    }

    private fun showHotSearchView(
        binding: FragmentSearchHomeBinding,
        operation: Operation,
        hotRankingsBean: RankingsBean?
    ) {

        val songs = hotRankingsBean?.songs

        when (operation) {
            Operation.NewData -> {
                mSearchHotAdapter.removeHeaderView(mHotSearchHeaderBinding.root)
                binding.llHotSearch.removeView(mHotSearchHeaderBinding.root)
                if (songs.isNullOrEmpty()) {//没有数据
                    if (mViewModel?.mHistoryInfoList?.value.isNullOrEmpty()) {
                        binding.slHotSearch.setEmptyIvTopMargin(resources.getDimension(R.dimen.dp_100))
                    } else {
                        binding.slHotSearch.setEmptyIvTopMargin(resources.getDimension(R.dimen.dp_20))
                    }
                    binding.slHotSearch.showEmpty()
                    binding.refreshLayoutHotSearch.setEnableLoadMore(false)
                    binding.recyclerviewHotSearch.setHasMoreData(false)
                    binding.llHotSearch.addView(mHotSearchHeaderBinding.root, 0)
                    Log.d(TAG, "HotSearch NewData: 数据为空，设置没有更多数据")
                } else {
                    binding.refreshLayoutHotSearch.setEnableLoadMore(true)
                    mSearchHotAdapter.addHeaderView(mHotSearchHeaderBinding.root)
                    mSearchHotAdapter.setNewInstance(songs)
                    binding.slHotSearch.showContent()
                    // 设置是否有更多数据
                    val hasMore = hotRankingsBean?.has_more == 1
                    binding.recyclerviewHotSearch.setHasMoreData(hasMore)
                    binding.refreshLayoutHotSearch.setNoMoreData(!hasMore)
                    Log.d(TAG, "HotSearch NewData: has_more = ${hotRankingsBean?.has_more}, 设置hasMoreData = $hasMore")
                }
            }

            Operation.LoadMore -> {
                if (songs.isNullOrEmpty()) {
                    if (hotRankingsBean?.has_more == 0) {
                        binding.refreshLayoutHotSearch.finishLoadMoreWithNoMoreData()
                        binding.recyclerviewHotSearch.setHasMoreData(false)
                        Log.d(TAG, "HotSearch LoadMore: 没有更多数据")
                    } else {
                        binding.refreshLayoutHotSearch.finishLoadMore(false)
                        Log.d(TAG, "HotSearch LoadMore: 加载失败")
                    }
                } else {
                    mSearchHotAdapter.addData(songs)
                    // 根据是否还有更多数据来决定状态
                    if (hotRankingsBean?.has_more == 1) {
                        binding.refreshLayoutHotSearch.finishLoadMore()
                        binding.recyclerviewHotSearch.setHasMoreData(true)
                        Log.d(TAG, "HotSearch LoadMore: 还有更多数据")
                    } else {
                        binding.refreshLayoutHotSearch.finishLoadMoreWithNoMoreData()
                        binding.recyclerviewHotSearch.setHasMoreData(false)
                        Log.d(TAG, "HotSearch LoadMore: 没有更多数据")
                    }
                }
            }

            Operation.UpdateStatus -> {
                mSearchHotAdapter.setList(songs)
            }

            else -> {}
        }

    }


    private fun showSearchResultView(
        binding: FragmentSearchHomeBinding,
        operation: Operation,
        searchResultBean: SearchResultBean,
    ) {
        Log.d(TAG, "showSearchResultView $operation")
        val songs = searchResultBean.songs
        val singers = searchResultBean.singers

        when (operation) {
            Operation.NewData -> {
                if (songs.isNullOrEmpty() && singers.isNullOrEmpty()) {
                    binding.slSearchResult.showEmpty()
                } else {
                    if (binding.viewPager.adapter == null) {
                        mSongsResultView = SearchSongsResultView(this.requireContext(), this)
                        mSongsResultView?.setHideKeyboardListener(object : HideKeyboardListener {
                            override fun hideKeyboard() {
                                hideInputMethod()
                            }
                        })
                        mSingerResultView = SearchSingersResultView(this.requireContext())
                        mSingerResultView?.setHideKeyboardListener(object : HideKeyboardListener {
                            override fun hideKeyboard() {
                                hideInputMethod()
                            }
                        })


                        val titles = listOf(
                            requireContext().getString(R.string.song),
                            requireContext().getString(R.string.singer)
                        )

                        mSongsResultView?.setOnLoadMoreListener {
                            mViewModel?.let { viewModel ->
                                Log.d(TAG, "loadMore songResult")
                                viewModel.mSearchText?.let { viewModel.getSearchResult(it, Operation.LoadMore) }
                            }
                        }

                        mSingerResultView?.setOnLoadMoreListener {
                            mViewModel?.let { viewModel ->
                                Log.d(TAG, "loadMore singerResult")
                                viewModel.mSearchText?.let { viewModel.getSearchResult(it, Operation.LoadMore) }
                            }
                        }

                        val searchResultPagerAdapter = SearchResultPagerAdapter(
                            requireActivity(),
                            titles
                        )

                        binding.viewPager.adapter = searchResultPagerAdapter

                        // 使用TabLayoutMediator连接TabLayout和ViewPager2
                        tabLayoutMediator = TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
                            tab.text = searchResultPagerAdapter.getPageTitle(position)
                        }
                        tabLayoutMediator?.attach()
                        clearTabLayoutTips(binding)

                    }
                    mSongsResultView?.setList(songs)
                    mSingerResultView?.setList(singers)
                    binding.tabLayout.getTabAt(0)?.select()
                    binding.slSearchResult.showContent()
                    binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                        override fun onTabSelected(tab: TabLayout.Tab) {
                            // 执行和父布局 onTouchListener 相同的逻辑
                            hideInputMethod()
                        }

                        override fun onTabUnselected(tab: TabLayout.Tab) {}

                        override fun onTabReselected(tab: TabLayout.Tab) {
                            // 执行和父布局 onTouchListener 相同的逻辑
                            hideInputMethod()
                        }
                    })
                }
            }

            Operation.LoadMore -> {
                mSongsResultView?.addData(songs)
                mSingerResultView?.addData(singers)
            }

            Operation.UpdateStatus -> {
                mSongsResultView?.notifyDataSetChanged()
            }

            else -> {}
        }

    }

    private fun clearTabLayoutTips(binding: FragmentSearchHomeBinding) {
        for (i in 0 until binding.tabLayout.tabCount) {
            val tab = binding.tabLayout.getTabAt(i)
            tab?.let {
                TooltipCompat.setTooltipText(it.view, null)
            }
        }
    }

    private fun showSuggestionView(
        binding: FragmentSearchHomeBinding,
        operation: Operation,
        searchResultBean: SearchResultBean
    ) {
        val singers = searchResultBean.singers
        val songs = searchResultBean.songs
        when (operation) {
            Operation.NewData -> {
                if (singers.isNullOrEmpty() && songs.isNullOrEmpty()) {
                    showSearchMain(binding)
                } else {
                    if (songs.isNullOrEmpty()) {
                        binding.refreshLayoutSuggestionSongs.setEnableLoadMore(false)
                        binding.recyclerviewSuggestionSongs.setHasMoreData(false)
                        Log.d(TAG, "Suggestion NewData: 歌曲数据为空，设置没有更多数据")
                    } else {
                        binding.refreshLayoutSuggestionSongs.setEnableLoadMore(searchResultBean.has_more)
                        binding.recyclerviewSuggestionSongs.setHasMoreData(searchResultBean.has_more)
                        binding.refreshLayoutSuggestionSongs.setNoMoreData(!searchResultBean.has_more)
                        Log.d(TAG, "Suggestion NewData: has_more = ${searchResultBean.has_more}, 设置hasMoreData = ${searchResultBean.has_more}")
                    }
                    initSuggestionSongsView(binding, songs)
                    initSuggestionSingersView(binding, singers)
                    showSuggestionSearchResult(binding)
                }
            }

            Operation.LoadMore -> {
                if (songs.isNullOrEmpty()) {
                    if (!searchResultBean.has_more) {
                        binding.refreshLayoutSuggestionSongs.finishLoadMoreWithNoMoreData()
                        binding.recyclerviewSuggestionSongs.setHasMoreData(false)
                        Log.d(TAG, "Suggestion LoadMore: 没有更多数据")
                    }
                } else {
                    mSuggestionSongAdapter.addData(songs)
                    // 根据是否还有更多数据来决定状态
                    if (searchResultBean.has_more) {
                        binding.refreshLayoutSuggestionSongs.finishLoadMore()
                        binding.recyclerviewSuggestionSongs.setHasMoreData(true)
                        Log.d(TAG, "Suggestion LoadMore: 还有更多数据")
                    } else {
                        binding.refreshLayoutSuggestionSongs.finishLoadMoreWithNoMoreData()
                        binding.recyclerviewSuggestionSongs.setHasMoreData(false)
                        Log.d(TAG, "Suggestion LoadMore: 没有更多数据")
                    }
                }
            }

            Operation.UpdateStatus -> {
                mSuggestionSongAdapter.setList(songs)
            }

            else -> {}
        }
    }

    private fun initSuggestionSingersView(
        binding: FragmentSearchHomeBinding,
        data: MutableList<SingerBean.Singer>?
    ) {
        if (data.isNullOrEmpty()) {
            mSearchSuggestionHeaderBinding.tvSuggestionSinger.visibility = GONE
            mSearchSuggestionHeaderBinding.recyclerviewSuggestionSingers.visibility = GONE
        } else {
            val singers = mutableListOf<SingerBean.Singer>()
            data.mapIndexed { index, singer ->
                if (index < SearchHomeViewModel.DEFAULT_SUGGESTION_SINGER_NUM) {
                    singers.add(singer)
                }
            }

            if (mSearchSuggestionHeaderBinding.recyclerviewSuggestionSingers.adapter == null) {
                mSearchSuggestionHeaderBinding.recyclerviewSuggestionSingers.addOnScrollListener(object :
                    OnScrollListener() {
                    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                        super.onScrollStateChanged(recyclerView, newState)
                        if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                            hideInputMethod()
                        }
                    }
                })

                val layoutManagerSinger =
                    AccessibilityGridLayoutManager(context, 4)
                mSearchSuggestionHeaderBinding.recyclerviewSuggestionSingers.layoutManager = layoutManagerSinger
                mSearchSuggestionHeaderBinding.recyclerviewSuggestionSingers.adapter = mSuggestionSingerAdapter
                mSuggestionSingerAdapter.setOnItemClickListener { adapter, v, position ->
                    hideInputMethod()
                    val singer = adapter.data[position] as SingerBean.Singer
                    val cardName =
                        BigDataConstants.CARD_NAME_SEARCH_SUGGESTION + SPLIT + getString(R.string.singer) + SPLIT + singer.singer_name
                    NavigationUtils.navigateSafely(
                        Navigation.findNavController(v),
                        R.id.action_search_to_song_by_singer,
                        SongListBySingerFragment.createBundle(singer.singer_id, singer.singer_name, cardName)
                    )
                }
            }

            mSuggestionSingerAdapter.setNewInstance(singers)

            mSearchSuggestionHeaderBinding.tvSuggestionSinger.visibility = VISIBLE
            mSearchSuggestionHeaderBinding.recyclerviewSuggestionSingers.visibility = VISIBLE

        }
    }

    private fun initSuggestionSongsView(
        binding: FragmentSearchHomeBinding,
        songs: MutableList<SongInfoBean>?
    ) {
//        if (songs.isNullOrEmpty()) {
//            binding.tvSuggestionSongs.visibility = GONE
//            binding.recyclerviewSuggestionSongs.visibility = GONE
//        } else {
        if (binding.recyclerviewSuggestionSongs.adapter == null) {
            binding.recyclerviewSuggestionSongs.addOnScrollListener(object :
                OnScrollListener() {
                override fun onScrollStateChanged(
                    recyclerView: RecyclerView,
                    newState: Int
                ) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState != RecyclerView.SCROLL_STATE_IDLE) {
                        hideInputMethod()
                    }
                }
            })
            val layoutManagerSong =
                AccessibilityLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            binding.recyclerviewSuggestionSongs.layoutManager = layoutManagerSong
            binding.recyclerviewSuggestionSongs.adapter = mSuggestionSongAdapter
            mSearchSuggestionHeaderBinding = LayoutSuggestionSearchHeaderBinding.inflate(
                LayoutInflater.from(context),
                mBinding?.root,
                false
            )
            mSuggestionSongAdapter.addHeaderView(mSearchSuggestionHeaderBinding.root)
            val suggestCardName = BigDataConstants.CARD_NAME_SEARCH_SUGGESTION + SPLIT + getString(R.string.song)
            mSuggestionSongAdapter.setCardName(suggestCardName)
            mSuggestionSongAdapter.setOnItemClickListener { _, view, position ->
                KaraokePlayerManager.playSong(
                    this,
                    mSuggestionSongAdapter.getItem(position), suggestCardName
                )
            }

            binding.refreshLayoutSuggestionSongs.setOnLoadMoreListener {
                mViewModel?.let { viewModel ->
                    Log.d(TAG, "loadMore suggestionResult")
                    viewModel.mSearchText?.let { viewModel.getSuggestionResult(it, Operation.LoadMore) }
                }

            }
        } else {
//                binding.layoutSuggestionResult.scrollTo(0, 0)
            binding.recyclerviewSuggestionSongs.scrollToPosition(0)
        }

        mSuggestionSongAdapter.setNewInstance(songs)
        if (songs.isNullOrEmpty()) {
            mSearchSuggestionHeaderBinding.tvSuggestionSongs.visibility = GONE
        } else {
            mSearchSuggestionHeaderBinding.tvSuggestionSongs.visibility = VISIBLE
        }
//            binding.recyclerviewSuggestionSongs.visibility = VISIBLE
//        }

    }


    override fun initObserve() {
        Log.d(TAG, "initObserve")
        mViewModel?.let { viewModel ->
            mBinding?.let { binding ->
                viewModel.mHintText.observe(viewLifecycleOwner) {
                    Log.d(TAG, "mHintText observe $it")
                    binding.etSearchBox.hint = it
                }

                viewModel.mHistoryInfoList.observe(viewLifecycleOwner) { historyList ->
                    Log.d(TAG, "mHistoryInfoList observe historylist")

                    if (historyList.isNullOrEmpty()) {
                        hideSearchHistoryView(binding)
                    } else {
                        showSearchHistoryView(binding, viewModel, historyList)
                    }

                }

                viewModel.mHotSearchBean.observe(viewLifecycleOwner) {

                    it.onSuccess { value, operation ->
                        showHotSearchView(binding, operation, value)
                    }.onFailure { resultCode, operation ->
                        when (operation) {
                            Operation.NewData -> {
                                mSearchHotAdapter.removeHeaderView(mHotSearchHeaderBinding.root)
                                binding.llHotSearch.removeView(mHotSearchHeaderBinding.root)
                                binding.llHotSearch.addView(mHotSearchHeaderBinding.root, 0)
                                if (mViewModel?.mHistoryInfoList?.value.isNullOrEmpty() == true) {
                                    binding.slHotSearch.setErrorIvTopMargin(resources.getDimension(R.dimen.dp_100))
                                } else {
                                    binding.slHotSearch.setErrorIvTopMargin(resources.getDimension(R.dimen.dp_20))
                                }
                                binding.slHotSearch.showError()
                                binding.refreshLayoutHotSearch.setEnableLoadMore(false)
                                setHotSearchErrorEntryListener()
                            }

                            Operation.LoadMore -> {
                                binding.refreshLayoutHotSearch.finishLoadMore(false)
                                showToast(R.string.load_more_failed)
                            }

                            else -> {}
                        }
                    }

                }

                viewModel.mSearchSuggestionResult.observe(viewLifecycleOwner) { it ->

                    it.onSuccess { value, operation ->
                        Log.d(TAG, "mSearchSuggestionResult.observe $value")

                        // 关键改进：检查是否应该响应联想搜索结果
                        if (!viewModel.shouldRespondToSuggestion()) {
                            Log.d(TAG, "正在进行正式搜索，忽略联想搜索结果")
                            return@onSuccess
                        }

                        // 检查当前状态是否为搜索结果页
                        if (showType == 3) {
                            Log.d(TAG, "当前在搜索结果页，忽略联想搜索结果")
                            return@onSuccess
                        }

                        if (value == null || mBinding?.etSearchBox?.text.isNullOrEmpty()) {
                            showSearchMain(binding)
                        } else {
                            showSuggestionView(binding, operation, value)
                        }
                    }.onFailure { resultCode, operation ->
                        // 只有在非正式搜索状态下才回到主页
                        if (viewModel.shouldRespondToSuggestion() && showType != 3) {
                            showSearchMain(binding)
                        }
                    }

                }

                viewModel.mSearchResultBean.observe(viewLifecycleOwner) {

                    it.onSuccess { value, operation ->
                        // 正式搜索成功后重置状态
                        viewModel.resetFormalSearchState()

                        if (value == null) {
                            binding.slSearchResult.showEmpty()
                        } else {
                            showSearchResultView(binding, operation, value)
                        }
                    }.onFailure { resultCode, operation ->
                        // 正式搜索失败后重置状态
                        viewModel.resetFormalSearchState()

                        when (operation) {
                            Operation.NewData -> {
                                binding.slSearchResult.showError()
                                setSearchResultErrorEntryListener()
                            }

                            Operation.LoadMore -> {
                                mSongsResultView?.finishLoadMore(false)
                                mSingerResultView?.finishLoadMore(false)
                                showToast(R.string.load_more_failed)
                            }

                            else -> {}
                        }
                    }
                }

                KaraokeConsole.playState.observe(this) {

                    when (showType) {
                        1 -> mSearchHotAdapter.notifyDataSetChanged()
                        2 -> mSuggestionSongAdapter.notifyDataSetChanged()
                        3 -> mSongsResultView?.notifyDataSetChanged()
                        else -> {}
                    }
                }

                viewModel.collectSongChangeLiveData.observe(viewLifecycleOwner) {

                    val data: MutableList<SongInfoBean>? = when (showType) {
                        1 -> mSearchHotAdapter.data
                        2 -> mSuggestionSongAdapter.data
                        3 -> mSongsResultView?.getData()
                        else -> {
                            return@observe
                        }
                    }
                    if (!data.isNullOrEmpty()) {
                        viewModel.updateCollectStatus(data, showType)
                    }
                }

                viewModel.demandSongInfo.observe(viewLifecycleOwner) {

                    val data: MutableList<SongInfoBean>? = when (showType) {
                        1 -> mSearchHotAdapter.data
                        2 -> mSuggestionSongAdapter.data
                        3 -> mSongsResultView?.getData()
                        else -> {
                            return@observe
                        }
                    }
                    if (!data.isNullOrEmpty()) {
                        viewModel.updateDemandStatus(data, showType)
                    }
                }

                KaraokePlayerManager.mIsShowPlayPage.observe(viewLifecycleOwner) { isShowPlayPage ->
                    backPressedCallback?.isEnabled = !(isShowPlayPage == true || !<EMAIL>)
                }

            }
        }


    }

    override fun initRequestData() {
        Log.d(TAG, "initRequestData")
        mViewModel?.let {
            it.getHotWords(Operation.NewData)
            it.getHistoryWords()
            it.getHotSearchList(Operation.NewData)
        }
    }

    private fun showInputMethod() {
        if (!mBinding?.etSearchBox?.text.isNullOrEmpty()) {
            mBinding?.ivSearchBoxClear?.visibility = VISIBLE
        }
        EventBusUtils.postEvent(PlayControlEvent(GONE))
        InputMethodUtils.showInputMethod(requireContext(), mBinding?.etSearchBox)
    }

    private fun hideInputMethod() {
        mBinding?.etSearchBox?.clearFocus()
        InputMethodUtils.hideInputMethod(requireContext(), mBinding?.etSearchBox)
        EventBusUtils.postEvent(PlayControlEvent(View.VISIBLE))
    }

    private fun clearInputText() {
        mBinding?.etSearchBox?.let {
            it.setText("")
            it.hint = mViewModel?.mHintText?.value
        }
        mBinding?.ivSearchBoxClear?.visibility = GONE
        mViewModel?.mSearchText = null
        hideInputMethod()
        // 清除EditText内容后，重新获取一遍搜索热词
        initRequestData()
    }

    override fun onDestroyView() {
        mBinding?.slSearchResult?.setErrorRetryClickListener(clickListener = null)
        mBinding?.slHotSearch?.setErrorRetryClickListener(clickListener = null)
        mSongsResultView?.setOnLoadMoreListener(null)
        mSongsResultView?.releaseFragment()
        mSingerResultView?.setOnLoadMoreListener(null)
        mSingerResultView?.release()
        // 清理TabLayoutMediator，防止内存泄露
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        mBinding?.viewPager?.adapter = null
        super.onDestroyView()

        mSongsResultView = null
        mSingerResultView = null
    }

    private fun goBack() {
        if (showType == 3) {
            mBinding?.let {
                mViewModel?.resetFormalSearchState()  // 重置正式搜索状态
                clearInputText()
                showSearchMain(it)
            }
        } else {
            findNavController().popBackStack()
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 创建返回键回调，初始状态为禁用，只有在Fragment可见时才启用
        backPressedCallback = object : OnBackPressedCallback(false) {
            override fun handleOnBackPressed() {
                // 拦截返回键
                goBack()
            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner, backPressedCallback!!)
    }

    override fun onResume() {
        super.onResume()
        // Fragment可见时启用返回键拦截
        backPressedCallback?.isEnabled = true
    }

    override fun onPause() {
        super.onPause()
        // Fragment不可见时禁用返回键拦截，避免影响其他Fragment的返回行为
        backPressedCallback?.isEnabled = false
    }

    companion object {
        private const val TAG = "SearchHomeFragment"
    }

}